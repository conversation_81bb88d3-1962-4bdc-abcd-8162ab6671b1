import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../models/database_table_model.dart';

/// تحسينات إضافية لتبويب إدارة قاعدة البيانات
class DatabaseManagementEnhancements {
  
  /// بناء مؤشر حالة الاتصال المحسن
  static Widget buildEnhancedConnectionStatus(DatabaseManagementController controller) {
    return Obx(() {
      final isConnected = controller.databaseInfo.isNotEmpty;
      final isLoading = controller.isLoading;
      
      if (isLoading) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.blue, width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
              const SizedBox(width: 6),
              Text(
                'جاري الاتصال...',
                style: AppStyles.bodySmall.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      }
      
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isConnected ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isConnected ? Colors.green : Colors.red,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isConnected ? Icons.check_circle : Icons.error,
              size: 16,
              color: isConnected ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 6),
            Text(
              isConnected ? 'متصل' : 'غير متصل',
              style: AppStyles.bodySmall.copyWith(
                color: isConnected ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء إحصائيات قاعدة البيانات
  static Widget buildDatabaseStats(DatabaseManagementController controller) {
    return Obx(() {
      final tables = controller.tables;
      final totalTables = tables.length;
      final totalRecords = tables.fold<int>(0, (sum, table) => sum + table.recordCount);
      
      return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.border),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات قاعدة البيانات',
              style: AppStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  icon: Icons.table_chart,
                  label: 'الجداول',
                  value: totalTables.toString(),
                  color: Colors.blue,
                ),
                _buildStatItem(
                  icon: Icons.storage,
                  label: 'السجلات',
                  value: totalRecords.toString(),
                  color: Colors.green,
                ),
                _buildStatItem(
                  icon: Icons.memory,
                  label: 'الحالة',
                  value: controller.databaseInfo.isNotEmpty ? 'نشط' : 'غير نشط',
                  color: controller.databaseInfo.isNotEmpty ? Colors.green : Colors.red,
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// بناء عنصر إحصائية
  static Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: AppStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// بناء شريط البحث المحسن
  static Widget buildEnhancedSearchBar({
    required TextEditingController searchController,
    required List<DatabaseColumn> searchableColumns,
    required String selectedColumn,
    required Function(String) onColumnChanged,
    required Function(String) onSearch,
    required VoidCallback onClear,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'البحث في البيانات',
            style: AppStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              // حقل البحث
              Expanded(
                flex: 3,
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث في البيانات...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: onClear,
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onSubmitted: onSearch,
                ),
              ),
              const SizedBox(width: 12),
              
              // قائمة اختيار العمود
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<String>(
                  value: selectedColumn,
                  decoration: InputDecoration(
                    labelText: 'البحث في',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  items: searchableColumns.map((column) {
                    return DropdownMenuItem<String>(
                      value: column.name,
                      child: Text(column.effectiveDisplayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      onColumnChanged(value);
                    }
                  },
                ),
              ),
              const SizedBox(width: 12),
              
              // زر البحث
              ElevatedButton.icon(
                onPressed: () => onSearch(searchController.text),
                icon: const Icon(Icons.search),
                label: const Text('بحث'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر التحميل المحسن
  static Widget buildEnhancedLoadingIndicator({
    required String message,
    bool showProgress = false,
    double? progress,
  }) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showProgress && progress != null)
              CircularProgressIndicator(
                value: progress,
                backgroundColor: AppColors.border,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              )
            else
              CircularProgressIndicator(
                backgroundColor: AppColors.border,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            if (showProgress && progress != null) ...[
              const SizedBox(height: 8),
              Text(
                '${(progress * 100).toInt()}%',
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textHint,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء شريط pagination متقدم
  static Widget buildAdvancedPagination(DatabaseManagementController controller) {
    return Obx(() {
      if (controller.tableData.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            top: BorderSide(color: AppColors.border),
          ),
        ),
        child: Row(
          children: [
            // معلومات الصفحة
            Expanded(
              child: Text(
                controller.pageInfo,
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),

            // أزرار التنقل
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // الصفحة الأولى
                IconButton(
                  onPressed: controller.canGoToPreviousPage
                      ? controller.goToFirstPage
                      : null,
                  icon: const Icon(Icons.first_page),
                  tooltip: 'الصفحة الأولى',
                ),

                // الصفحة السابقة
                IconButton(
                  onPressed: controller.canGoToPreviousPage
                      ? controller.previousPage
                      : null,
                  icon: const Icon(Icons.chevron_left),
                  tooltip: 'الصفحة السابقة',
                ),

                // رقم الصفحة الحالية
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${controller.currentPage} من ${controller.totalPages}',
                    style: AppStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),

                // الصفحة التالية
                IconButton(
                  onPressed: controller.canGoToNextPage
                      ? controller.nextPage
                      : null,
                  icon: const Icon(Icons.chevron_right),
                  tooltip: 'الصفحة التالية',
                ),

                // الصفحة الأخيرة
                IconButton(
                  onPressed: controller.canGoToNextPage
                      ? controller.goToLastPage
                      : null,
                  icon: const Icon(Icons.last_page),
                  tooltip: 'الصفحة الأخيرة',
                ),
              ],
            ),

            const SizedBox(width: 16),

            // اختيار حجم الصفحة
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'عرض:',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: controller.pageSize,
                  items: [10, 25, 50, 100, 200].map((size) {
                    return DropdownMenuItem(
                      value: size,
                      child: Text('$size'),
                    );
                  }).toList(),
                  onChanged: (newSize) {
                    if (newSize != null) {
                      controller.changePageSize(newSize);
                    }
                  },
                  underline: const SizedBox.shrink(),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// بناء شريط إحصائيات الجدول
  static Widget buildTableStats(DatabaseTable table, DatabaseManagementController controller) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            bottom: BorderSide(color: AppColors.border),
          ),
        ),
        child: Row(
          children: [
            // أيقونة الجدول
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.table_chart,
                color: AppColors.primary,
                size: 20,
              ),
            ),

            const SizedBox(width: 12),

            // معلومات الجدول
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    table.displayName,
                    style: AppStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      _buildStatChip(
                        icon: Icons.view_column,
                        label: '${table.columns.length} عمود',
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildStatChip(
                        icon: Icons.table_rows,
                        label: '${controller.totalRecords} سجل',
                        color: Colors.green,
                      ),
                      if (controller.searchQuery.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        _buildStatChip(
                          icon: Icons.search,
                          label: 'مفلتر',
                          color: Colors.orange,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء chip للإحصائيات
  static Widget _buildStatChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
